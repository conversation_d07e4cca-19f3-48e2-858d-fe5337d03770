import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';

const app = express();
const server = createServer(app);

// Configurar CORS para permitir conexiones desde el frontend
const io = new Server(server, {
  cors: {
    origin: ["http://localhost:5173", "http://localhost:3000", "https://prompt-arena-challenge.vercel.app"],
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// Almacenamiento en memoria para las salas de juego
const gameRooms = new Map();

// Sala global única para todos los jugadores
const GLOBAL_ROOM_ID = 'BATALLA_GLOBAL';

// Estructura de una sala de juego
class GameRoom {
  constructor(roomId) {
    this.roomId = roomId;
    this.participants = [];
    this.currentPhase = 'registration'; // 'registration', 'waiting', 'arena', 'results'
    this.currentChallenge = 0;
    this.timeLeft = 0;
    this.isActive = false;
    this.createdAt = new Date();
    this.gameTimer = null;
    this.challengeTimeLimit = 180; // 3 minutos por defecto
  }

  addParticipant(socketId, playerName) {
    const participant = {
      id: socketId,
      socketId: socketId,
      name: playerName,
      score: 0,
      isHost: this.participants.length === 0,
      joinedAt: new Date()
    };
    this.participants.push(participant);
    return participant;
  }

  removeParticipant(socketId) {
    const index = this.participants.findIndex(p => p.socketId === socketId);
    if (index !== -1) {
      const removedParticipant = this.participants.splice(index, 1)[0];
      
      // Si el host se va, asignar a otro participante
      if (removedParticipant.isHost && this.participants.length > 0) {
        this.participants[0].isHost = true;
      }
      
      return removedParticipant;
    }
    return null;
  }

  updateScore(participantId, points) {
    const participant = this.participants.find(p => p.id === participantId);
    if (participant) {
      participant.score += points;
      return participant;
    }
    return null;
  }

  getGameState() {
    return {
      roomId: this.roomId,
      participants: this.participants,
      currentPhase: this.currentPhase,
      currentChallenge: this.currentChallenge,
      timeLeft: this.timeLeft,
      isActive: this.isActive
    };
  }

  startChallenge(io) {
    this.isActive = true;
    this.timeLeft = this.challengeTimeLimit;

    // Limpiar timer anterior si existe
    if (this.gameTimer) {
      clearInterval(this.gameTimer);
    }

    // Iniciar timer del servidor
    this.gameTimer = setInterval(() => {
      this.timeLeft--;

      // Enviar actualización de tiempo a todos los clientes
      io.to(this.roomId).emit('time-updated', this.timeLeft);

      if (this.timeLeft <= 0) {
        this.endChallenge(io);
      }
    }, 1000);

    // Notificar inicio del desafío
    io.to(this.roomId).emit('challenge-started', {
      challengeIndex: this.currentChallenge,
      timeLeft: this.timeLeft
    });
    io.to(this.roomId).emit('game-state-updated', this.getGameState());
  }

  endChallenge(io) {
    this.isActive = false;

    // Limpiar timer
    if (this.gameTimer) {
      clearInterval(this.gameTimer);
      this.gameTimer = null;
    }

    // Notificar fin del desafío
    io.to(this.roomId).emit('challenge-ended');
    io.to(this.roomId).emit('game-state-updated', this.getGameState());
  }

  nextChallenge(io) {
    this.currentChallenge++;

    if (this.currentChallenge >= 4) {
      this.currentPhase = 'results';
      this.isActive = false;
      if (this.gameTimer) {
        clearInterval(this.gameTimer);
        this.gameTimer = null;
      }
    }

    io.to(this.roomId).emit('game-state-updated', this.getGameState());
  }

  resetGame(io) {
    this.currentPhase = 'waiting';
    this.currentChallenge = 0;
    this.isActive = false;
    this.timeLeft = 0;
    this.participants.forEach(p => p.score = 0);

    if (this.gameTimer) {
      clearInterval(this.gameTimer);
      this.gameTimer = null;
    }

    io.to(this.roomId).emit('game-state-updated', this.getGameState());
  }
}

// Función para generar ID de sala único
function generateRoomId() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Función para limpiar salas vacías (ejecutar cada 30 minutos)
function cleanupEmptyRooms() {
  const now = new Date();
  for (const [roomId, room] of gameRooms.entries()) {
    // Eliminar salas vacías que tengan más de 30 minutos
    if (room.participants.length === 0 && (now - room.createdAt) > 30 * 60 * 1000) {
      gameRooms.delete(roomId);
      console.log(`Sala ${roomId} eliminada por inactividad`);
    }
  }
}

// Limpiar salas cada 30 minutos
setInterval(cleanupEmptyRooms, 30 * 60 * 1000);

// Crear la sala global al iniciar el servidor
if (!gameRooms.has(GLOBAL_ROOM_ID)) {
  gameRooms.set(GLOBAL_ROOM_ID, new GameRoom(GLOBAL_ROOM_ID));
  console.log(`🌍 Sala global creada: ${GLOBAL_ROOM_ID}`);
}

io.on('connection', (socket) => {
  console.log(`Usuario conectado: ${socket.id}`);

  // Unirse a una sala (siempre será la sala global)
  socket.on('join-room', ({ roomId, playerName }) => {
    try {
      console.log(`${playerName} intentando unirse a la batalla global`);

      // Siempre usar la sala global, ignorar roomId enviado
      const globalRoomId = GLOBAL_ROOM_ID;

      // La sala global ya existe, no necesitamos crearla
      const room = gameRooms.get(globalRoomId);
      
      // Verificar si el jugador ya está en la sala
      const existingParticipant = room.participants.find(p => p.socketId === socket.id);
      if (existingParticipant) {
        socket.emit('error', 'Ya estás en esta sala');
        return;
      }

      // Verificar si la sala está en juego
      if (room.currentPhase === 'arena') {
        socket.emit('error', 'La batalla está en curso, espera a que termine para unirte');
        return;
      }

      // Unirse a la sala de Socket.IO
      socket.join(globalRoomId);

      // Agregar participante a la sala
      const participant = room.addParticipant(socket.id, playerName);

      // Cambiar fase a 'waiting' si hay al menos un participante
      if (room.participants.length >= 1 && room.currentPhase === 'registration') {
        room.currentPhase = 'waiting';
      }

      // Notificar a todos en la sala
      io.to(globalRoomId).emit('participant-joined', participant);
      io.to(globalRoomId).emit('game-state-updated', room.getGameState());

      console.log(`${playerName} se unió a la batalla global. Total participantes: ${room.participants.length}`);

    } catch (error) {
      console.error('Error al unirse a la sala:', error);
      socket.emit('error', 'Error al unirse a la sala');
    }
  });

  // Iniciar juego
  socket.on('start-game', (roomId) => {
    try {
      const room = gameRooms.get(roomId);
      if (!room) {
        socket.emit('error', 'Sala no encontrada');
        return;
      }

      const participant = room.participants.find(p => p.socketId === socket.id);
      if (!participant || !participant.isHost) {
        socket.emit('error', 'Solo el host puede iniciar el juego');
        return;
      }

      if (room.participants.length < 1) {
        socket.emit('error', 'Se necesita al menos 1 participante para iniciar');
        return;
      }

      room.currentPhase = 'arena';
      room.currentChallenge = 0;

      // Iniciar el primer desafío con timer del servidor
      room.startChallenge(io);

      console.log(`Juego iniciado en sala ${roomId} por ${participant.name}`);

    } catch (error) {
      console.error('Error al iniciar juego:', error);
      socket.emit('error', 'Error al iniciar el juego');
    }
  });

  // Actualizar puntuación
  socket.on('update-score', ({ roomId, participantId, points }) => {
    try {
      const room = gameRooms.get(roomId);
      if (!room) {
        socket.emit('error', 'Sala no encontrada');
        return;
      }

      const updatedParticipant = room.updateScore(participantId, points);
      if (updatedParticipant) {
        io.to(roomId).emit('score-updated', {
          participantId,
          newScore: updatedParticipant.score
        });
        io.to(roomId).emit('game-state-updated', room.getGameState());

        console.log(`Puntuación actualizada en sala ${roomId}: ${updatedParticipant.name} +${points} = ${updatedParticipant.score}`);
      }

    } catch (error) {
      console.error('Error al actualizar puntuación:', error);
      socket.emit('error', 'Error al actualizar puntuación');
    }
  });

  // Iniciar desafío (solo host)
  socket.on('start-challenge', (roomId) => {
    try {
      const room = gameRooms.get(roomId);
      if (!room) {
        socket.emit('error', 'Sala no encontrada');
        return;
      }

      const participant = room.participants.find(p => p.socketId === socket.id);
      if (!participant || !participant.isHost) {
        socket.emit('error', 'Solo el host puede iniciar desafíos');
        return;
      }

      room.startChallenge(io);
      console.log(`Desafío ${room.currentChallenge} iniciado en sala ${roomId} por ${participant.name}`);

    } catch (error) {
      console.error('Error al iniciar desafío:', error);
      socket.emit('error', 'Error al iniciar desafío');
    }
  });

  // Siguiente desafío (solo host)
  socket.on('next-challenge', (roomId) => {
    try {
      const room = gameRooms.get(roomId);
      if (!room) {
        socket.emit('error', 'Sala no encontrada');
        return;
      }

      const participant = room.participants.find(p => p.socketId === socket.id);
      if (!participant || !participant.isHost) {
        socket.emit('error', 'Solo el host puede avanzar desafíos');
        return;
      }

      room.nextChallenge(io);

      // Si no es el último desafío, iniciar el siguiente automáticamente
      if (room.currentChallenge < 4 && room.currentPhase === 'arena') {
        setTimeout(() => {
          room.startChallenge(io);
        }, 2000); // 2 segundos de pausa entre desafíos
      }

      console.log(`Siguiente desafío en sala ${roomId}: ${room.currentChallenge}`);

    } catch (error) {
      console.error('Error al avanzar desafío:', error);
      socket.emit('error', 'Error al avanzar desafío');
    }
  });

  // Finalizar desafío (solo host)
  socket.on('end-challenge', (roomId) => {
    try {
      const room = gameRooms.get(roomId);
      if (!room) {
        socket.emit('error', 'Sala no encontrada');
        return;
      }

      const participant = room.participants.find(p => p.socketId === socket.id);
      if (!participant || !participant.isHost) {
        socket.emit('error', 'Solo el host puede finalizar desafíos');
        return;
      }

      room.endChallenge(io);
      console.log(`Desafío finalizado en sala ${roomId} por ${participant.name}`);

    } catch (error) {
      console.error('Error al finalizar desafío:', error);
      socket.emit('error', 'Error al finalizar desafío');
    }
  });

  // Reiniciar juego (solo host)
  socket.on('reset-game', (roomId) => {
    try {
      const room = gameRooms.get(roomId);
      if (!room) {
        socket.emit('error', 'Sala no encontrada');
        return;
      }

      const participant = room.participants.find(p => p.socketId === socket.id);
      if (!participant || !participant.isHost) {
        socket.emit('error', 'Solo el host puede reiniciar el juego');
        return;
      }

      room.resetGame(io);
      console.log(`Juego reiniciado en sala ${roomId} por ${participant.name}`);

    } catch (error) {
      console.error('Error al reiniciar juego:', error);
      socket.emit('error', 'Error al reiniciar juego');
    }
  });

  // Desconexión
  socket.on('disconnect', () => {
    console.log(`Usuario desconectado: ${socket.id}`);

    // Buscar y remover el participante de todas las salas
    for (const [roomId, room] of gameRooms.entries()) {
      const removedParticipant = room.removeParticipant(socket.id);
      if (removedParticipant) {
        io.to(roomId).emit('participant-left', removedParticipant.id);
        io.to(roomId).emit('game-state-updated', room.getGameState());
        
        console.log(`${removedParticipant.name} salió de la sala ${roomId}`);
        
        // Si la sala queda vacía, cambiar fase a registration
        if (room.participants.length === 0) {
          room.currentPhase = 'registration';
        }
        break;
      }
    }
  });
});

// Endpoint para obtener información de salas (opcional, para debugging)
app.get('/api/rooms', (req, res) => {
  const roomsInfo = Array.from(gameRooms.entries()).map(([roomId, room]) => ({
    roomId,
    participantCount: room.participants.length,
    currentPhase: room.currentPhase,
    isActive: room.isActive,
    createdAt: room.createdAt
  }));
  res.json(roomsInfo);
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Servidor Socket.IO ejecutándose en puerto ${PORT}`);
  console.log(`🌐 Frontend puede conectarse desde: http://localhost:5173`);
});
