import { io, Socket } from 'socket.io-client';
import { Participant } from '@/pages/Index';

export interface GameState {
  participants: Participant[];
  currentPhase: 'registration' | 'waiting' | 'arena' | 'results';
  currentChallenge?: number;
  timeLeft?: number;
  isActive?: boolean;
  roomId: string;
}

export interface SocketEvents {
  // Client to Server
  'join-room': (data: { roomId: string; playerName: string }) => void;
  'start-game': (roomId: string) => void;
  'start-challenge': (roomId: string) => void;
  'update-score': (data: { roomId: string; participantId: string; points: number }) => void;
  'next-challenge': (roomId: string) => void;
  'end-challenge': (roomId: string) => void;
  'reset-game': (roomId: string) => void;

  // Server to Client
  'game-state-updated': (gameState: GameState) => void;
  'participant-joined': (participant: Participant) => void;
  'participant-left': (participantId: string) => void;
  'challenge-started': (data: { challengeIndex: number; timeLeft: number }) => void;
  'challenge-ended': () => void;
  'time-updated': (timeLeft: number) => void;
  'score-updated': (data: { participantId: string; newScore: number }) => void;
  'error': (message: string) => void;
}

class SocketService {
  private socket: Socket | null = null;
  private roomId: string | null = null;
  private isConnected: boolean = false;

  // URL del servidor real
  private readonly SERVER_URL = process.env.NODE_ENV === 'production'
    ? 'https://prompt-arena-server.vercel.app'
    : 'http://localhost:3001';

  connect(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      try {
        // Conectar al servidor real de Socket.IO
        this.socket = io(this.SERVER_URL, {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          forceNew: true
        });

        this.socket.on('connect', () => {
          console.log('✅ Conectado al servidor Socket.IO:', this.socket?.id);
          this.isConnected = true;
          resolve(this.socket!);
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Error de conexión Socket.IO:', error);
          this.isConnected = false;
          reject(error);
        });

        this.socket.on('disconnect', (reason) => {
          console.log('🔌 Desconectado del servidor:', reason);
          this.isConnected = false;
        });

        this.socket.on('error', (error) => {
          console.error('❌ Error del servidor:', error);
        });

      } catch (error) {
        console.error('❌ Error al crear conexión Socket.IO:', error);
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.roomId = null;
    }
  }

  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  joinRoom(roomId: string, playerName: string) {
    this.roomId = roomId;
    if (this.socket) {
      this.socket.emit('join-room', { roomId, playerName });
    }
  }

  startGame() {
    if (this.socket && this.roomId) {
      this.socket.emit('start-game', this.roomId);
    }
  }

  startChallenge() {
    if (this.socket && this.roomId) {
      this.socket.emit('start-challenge', this.roomId);
    }
  }

  updateScore(participantId: string, points: number) {
    if (this.socket && this.roomId) {
      this.socket.emit('update-score', { 
        roomId: this.roomId, 
        participantId, 
        points 
      });
    }
  }

  nextChallenge() {
    if (this.socket && this.roomId) {
      this.socket.emit('next-challenge', this.roomId);
    }
  }

  endChallenge() {
    if (this.socket && this.roomId) {
      this.socket.emit('end-challenge', this.roomId);
    }
  }

  resetGame() {
    if (this.socket && this.roomId) {
      this.socket.emit('reset-game', this.roomId);
    }
  }

  onGameStateUpdated(callback: (gameState: GameState) => void) {
    if (this.socket) {
      this.socket.on('game-state-updated', callback);
    }
  }

  onParticipantJoined(callback: (participant: Participant) => void) {
    if (this.socket) {
      this.socket.on('participant-joined', callback);
    }
  }

  onScoreUpdated(callback: (data: { participantId: string; newScore: number }) => void) {
    if (this.socket) {
      this.socket.on('score-updated', callback);
    }
  }

  onChallengeStarted(callback: (data: { challengeIndex: number; timeLeft: number }) => void) {
    if (this.socket) {
      this.socket.on('challenge-started', callback);
    }
  }

  onChallengeEnded(callback: () => void) {
    if (this.socket) {
      this.socket.on('challenge-ended', callback);
    }
  }

  onTimeUpdated(callback: (timeLeft: number) => void) {
    if (this.socket) {
      this.socket.on('time-updated', callback);
    }
  }

  onError(callback: (message: string) => void) {
    if (this.socket) {
      this.socket.on('error', callback);
    }
  }
}

export const socketService = new SocketService();
