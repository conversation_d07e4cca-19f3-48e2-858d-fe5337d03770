
import React, { useState, useEffect } from 'react';
import Registration from '@/components/Registration';
import WaitingRoom from '@/components/WaitingRoom';
import ChallengeArena from '@/components/ChallengeArena';
import FinalResults from '@/components/FinalResults';
import { socketService, GameState } from '@/services/socketService';

export type GamePhase = 'registration' | 'waiting' | 'arena' | 'results';

export interface Participant {
  id: string;
  name: string;
  score: number;
  isHost?: boolean;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  timeLimit: number;
  type: 'prompt' | 'human-ai' | 'errors' | 'prompt-choice' | 'prompt-evaluation';
}

const Index = () => {
  const [currentPhase, setCurrentPhase] = useState<GamePhase>('registration');
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [currentUser, setCurrentUser] = useState<Participant | null>(null);
  const [isMultiplayer, setIsMultiplayer] = useState(false);
  const [roomId, setRoomId] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [gameState, setGameState] = useState<GameState | null>(null);

  // Initialize socket connection
  useEffect(() => {
    const initSocket = async () => {
      try {
        await socketService.connect();
        setIsConnected(true);

        // Setup socket event listeners
        socketService.onGameStateUpdated((gameState: GameState) => {
          setParticipants(gameState.participants);
          setCurrentPhase(gameState.currentPhase);
          setGameState(gameState);
        });

        socketService.onParticipantJoined((participant: Participant) => {
          setParticipants(prev => [...prev, participant]);
        });

        socketService.onScoreUpdated(({ participantId, newScore }) => {
          setParticipants(prev =>
            prev.map(p => p.id === participantId ? { ...p, score: newScore } : p)
          );
        });

      } catch (error) {
        console.error('Failed to connect to socket:', error);
        setIsConnected(false);
      }
    };

    initSocket();

    return () => {
      socketService.disconnect();
    };
  }, []);

  const handleRegistration = (name: string, multiplayer = true) => {
    setIsMultiplayer(true); // Always multiplayer

    if (isConnected) {
      // Siempre usar la sala global
      const globalRoomId = 'BATALLA_GLOBAL';
      setRoomId(globalRoomId);

      // Join multiplayer room via socket
      socketService.joinRoom(globalRoomId, name);

      // Create user participant for local state
      const newParticipant: Participant = {
        id: Date.now().toString(),
        name,
        score: 0,
        isHost: participants.length === 0
      };
      setCurrentUser(newParticipant);
    } else {
      // Fallback if socket not connected
      console.error('Socket not connected, retrying...');
      // Retry connection
      setTimeout(() => handleRegistration(name, multiplayer), 1000);
    }
  };

  const startGame = () => {
    if (isConnected) {
      socketService.startGame();
    } else {
      console.error('Cannot start game: Socket not connected');
    }
  };

  const endGame = () => {
    if (isConnected) {
      socketService.endChallenge();
    } else {
      setCurrentPhase('results');
    }
  };

  const resetGame = () => {
    if (isConnected) {
      socketService.resetGame();
    } else {
      setCurrentPhase('registration');
      setParticipants([]);
      setCurrentUser(null);
    }
  };

  const updateScore = (participantId: string, points: number) => {
    if (isConnected) {
      socketService.updateScore(participantId, points);
    } else {
      // Fallback for local update
      setParticipants(prev =>
        prev.map(p => p.id === participantId ? { ...p, score: p.score + points } : p)
      );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-6xl md:text-7xl font-title bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent mb-4 text-game-title animate-pulse-glow text-ultra-contrast">
            🚀 PROMPT BATTLE
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 font-bold text-game-subtitle text-ultra-contrast">
            Desafíos épicos de IA • Compite • Aprende • Gana
          </p>
          <div className="mt-4 text-sm md:text-base text-gray-300 font-semibold text-ultra-contrast">
            ⚡ La arena definitiva para maestros de prompts ⚡
          </div>
        </div>

        {/* Phase Content */}
        {currentPhase === 'registration' && (
          <Registration onRegister={handleRegistration} />
        )}

        {currentPhase === 'waiting' && (
          <WaitingRoom
            participants={participants}
            currentUser={currentUser}
            onStartGame={startGame}
            roomId={roomId}
          />
        )}

        {currentPhase === 'arena' && (
          <ChallengeArena
            participants={participants}
            setParticipants={setParticipants}
            onGameEnd={endGame}
            currentUser={currentUser}
            onUpdateScore={updateScore}
            gameState={gameState ? {
              currentChallenge: gameState.currentChallenge ?? 0,
              timeLeft: gameState.timeLeft ?? 180,
              isActive: gameState.isActive ?? false
            } : undefined}
          />
        )}

        {currentPhase === 'results' && (
          <FinalResults 
            participants={participants}
            onResetGame={resetGame}
          />
        )}
      </div>
    </div>
  );
};

export default Index;
