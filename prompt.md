# Prompt Arena Challenge - Desafíos Implementados

## 🌍 Arena Global Multijugador
Una plataforma de competencia en tiempo real donde todos los jugadores luchan en la misma arena global usando Socket.IO para comunicación en tiempo real.

## 🎮 Estructura del Juego

### Fases del Juego:
1. **Registro**: Los participantes ingresan su nombre
2. **Sala de Espera**: Muestra jugadores conectados, el host puede iniciar el juego
3. **Arena de Desafíos**: 5 desafíos consecutivos con tiempo límite
4. **Resultados Finales**: Ranking y puntuaciones finales

## 🏆 Desafíos Implementados

### 1. 💻 Evaluación de Prompt Técnico (5 minutos)
**Tipo**: `prompt-evaluation`
**Descripción**: Crea un prompt para generar una aplicación de C# que consulte el Dólar Blue argentino
- Los participantes escriben un prompt técnico específico
- La IA de Gemini evalúa la calidad del prompt con un score de 0-100
- Se evalúa claridad, especificidad técnica y completitud

### 2. 📚 Información de Personajes (3 minutos)
**Tipo**: `prompt`
**Descripción**: Obtén información sobre personajes de Rick and Morty usando prompts efectivos
- Desafío: Crear el mejor prompt para obtener información de la API de Rick and Morty
- Debe incluir: nombres, especies, origen, descripción, estado y episodios
- La IA de Gemini ejecuta el prompt y evalúa la calidad de la respuesta
- Puntuación basada en completitud y precisión de la información obtenida

### 3. 🤖 ¿Humano o IA? (3 minutos)
**Tipo**: `human-ai`
**Descripción**: Identifica qué contenido fue creado por humanos y cuál por IA
- Se presentan 5 fragmentos de código
- Los participantes deben identificar si cada fragmento fue escrito por humano o IA
- Incluye ejemplos de:
  - Código con patrones típicos de IA (repetitivo, genérico)
  - Código humano con estilo personal y comentarios específicos
  - Código con errores sutiles típicos de IA
- 20 puntos por cada respuesta correcta

### 4. 🔍 Cazador de Alucinaciones (3 minutos)
**Tipo**: `errors`
**Descripción**: Encuentra funciones y métodos inventados por la IA
- Se muestra código generado por IA con errores sutiles y alucinaciones
- Los participantes deben identificar:
  - Métodos inexistentes (ej: `detectLanguageAzure` en cliente Gemini)
  - APIs incorrectas (ej: usar API key de OpenAI con cliente Gemini)
  - Funciones inventadas o mal implementadas
- Ejemplos incluyen confusiones entre servicios (Azure, OpenAI, Gemini)
- Puntuación basada en número de errores encontrados correctamente

### 5. 🎯 Elige el Mejor Prompt (3 minutos)
**Tipo**: `prompt-choice`
**Descripción**: Selecciona los prompts más efectivos para obtener mejores resultados
- Se presentan escenarios de desarrollo común
- Para cada escenario, hay 3-4 opciones de prompts
- Los participantes deben elegir el prompt más efectivo
- Escenarios incluyen:
  - Documentación de código
  - Debugging y resolución de errores
  - Explicación de conceptos técnicos
  - Optimización de código
  - Generación de tests unitarios
- 30 puntos por cada elección correcta

## ⚙️ Características Técnicas

### Sistema de Puntuación:
- **Prompt Evaluation**: 0-100 puntos (evaluado por IA)
- **Prompt Battle**: 0-100 puntos (evaluado por IA)
- **Human vs AI**: 20 puntos por respuesta correcta (máximo 100)
- **Error Detection**: Puntos variables según errores encontrados
- **Prompt Choice**: 30 puntos por respuesta correcta

### Multijugador en Tiempo Real:
- Socket.IO para comunicación en tiempo real
- Una sola arena global para todos los jugadores
- El host controla el flujo del juego
- Sincronización de temporizadores entre todos los participantes

### Interfaz Gaming:
- Estética arcade con alta legibilidad
- Animaciones y efectos visuales
- Temporizadores visibles y dinámicos
- Colores contrastantes para accesibilidad
- Diseño responsivo para diferentes dispositivos

## 📝 Ejercicios y Configuraciones Específicas

### 🔧 Desafío 1: Evaluación de Prompt Técnico

**Consigna Exacta:**
```
Crea un prompt para que una IA genere una aplicación de consola en **C#** que consulte la cotización del Dólar Blue en Argentina, obteniendo los datos de una API pública como `bluelytics.com.ar`. Tu prompt debe indicar que la aplicación, tras obtener los valores de compra, venta y la fecha de actualización, debe devolver toda esa información junta en un único formato **JSON**. El desafío es que tu prompt sea tan preciso que la IA infiera por sí misma una estructura JSON lógica y coherente para la salida, sin que tú se la definas.
```

**Criterios de Evaluación por IA (Gemini):**
- **A. Especificidad Técnica (40 puntos):**
  - Lenguaje de programación (10 pts): ¿Menciona explícitamente C#?
  - Tipo de aplicación (10 pts): ¿Especifica que debe ser una aplicación de consola?
  - API específica (20 pts): ¿Menciona bluelytics.com.ar o APIs similares del Dólar Blue?

- **B. Cumplimiento de Requisitos Funcionales (50 puntos):**
  - Extracción de Datos (20 pts): ¿Solicita explícitamente valor de compra, venta y fecha?
  - Formato de Salida (30 pts): ¿Instruye CLARAMENTE que la salida debe ser JSON?

- **C. Calidad General del Prompt (10 puntos):**
  - Concisión y Eficacia (10 pts): ¿Es directo, sin ambigüedades?

### 🎮 Desafío 2: Información de Personajes (Rick and Morty)

**Consigna Exacta:**
```
Quiero que obtengas los personajes principales de Rick and Morty de esta API: https://rickandmortyapi.com/api/character

Por favor, ayúdame a crear un prompt que me permita obtener:

1. Los nombres de los 5 personajes principales
2. Su especie y origen
3. Una breve descripción de cada uno
4. Su estado (vivo, muerto, desconocido)
5. En qué episodios aparecen más frecuentemente

Escribe un prompt claro y directo que pueda usar con cualquier IA para obtener esta información de manera organizada y fácil de entender.
```

**Criterios de Evaluación por IA (Gemini):**
- Claridad y especificidad del prompt (30 puntos)
- Facilidad de comprensión para una IA (25 puntos)
- Organización y estructura (25 puntos)
- Creatividad y detalle solicitado (20 puntos)

### 🤖 Desafío 3: ¿Humano o IA? - Fragmentos de Código

**Fragmento #1 (HUMANO):**
```javascript
function quickSort(arr) {
  if (arr.length <= 1) return arr;

  const pivot = arr[Math.floor(arr.length / 2)];
  const left = arr.filter(x => x < pivot);
  const right = arr.filter(x => x > pivot);

  return [...quickSort(left), pivot, ...quickSort(right)];
}
```
*Explicación: Estilo humano con nombres descriptivos y estructura clara*

**Fragmento #2 (IA):**
```javascript
const calculateFactorial = (n) => {
  if (n === 0 || n === 1) {
    return 1;
  }
  return n * calculateFactorial(n - 1);
};
```
*Explicación: Estilo IA con estructura muy textbook y comentarios típicos*

**Fragmento #3 (HUMANO):**
```javascript
// Messy human code with quirks
let data = [];
for(let i=0;i<items.length;i++){
    if(items[i].status=='active'){
        data.push(items[i]);
    }
}
return data;
```
*Explicación: Claramente humano con inconsistencias en spacing y estilo*

**Fragmento #4 (IA):**
```javascript
/**
 * This function validates email addresses using regular expressions
 * @param {string} email - The email address to validate
 * @returns {boolean} - True if valid, false otherwise
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```
*Explicación: Típico IA con documentación perfecta y estructura muy formal*

**Fragmento #5 (HUMANO):**
```javascript
const handleClick = (e) => {
  e.preventDefault();
  // TODO: fix this later
  console.log('clicked');
  doSomething();
};
```
*Explicación: Humano con comentarios informales y console.log para debug*

### 🔍 Desafío 4: Cazador de Alucinaciones - Código con Errores

**Código con Alucinaciones:**
```javascript
async function summarizeArticleWithGemini(articleText) {
  const client = new GeminiApiClient({
    apiKey: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  });

  const serviceStatus = await client.checkStatus();
  if (!serviceStatus.isOperational) {
    throw new Error('Servicio Gemini no disponible.');
  }

  const detectedLanguage = await client.detectLanguageAzure(articleText.substring(0, 50));

  let targetModel = 'gemini-pro-summarizer';

  if (detectedLanguage.code !== 'en') {
    targetModel = 'azure-cognitive-translator/english-variant';
  }

  const summary = await client.generateSummary(articleText, {
    model: targetModel,
    engine: 'text-davinci-003',
  });

  return summary.text;
}
```

**Errores a Encontrar:**

1. **Línea 7:** API Key de OpenAI (formato 'sk-...') usada con cliente Gemini
   - *Explicación: El cliente Gemini esperaría una API Key de Google*

2. **Línea 16:** Método 'detectLanguageAzure' inexistente en cliente Gemini
   - *Explicación: La IA inventó un método con sufijo 'Azure' para Gemini*

3. **Línea 22:** Identificador de modelo de Azure en Gemini
   - *Explicación: 'azure-cognitive-translator/...' no es válido para Gemini*

4. **Línea 27:** Parámetro 'engine' y modelo de OpenAI en Gemini
   - *Explicación: 'text-davinci-003' es específico de OpenAI, no Gemini*

### 🎯 Desafío 5: Elige el Mejor Prompt - Escenarios de Desarrollo

**Tarea #1: Generar una función JavaScript para validar emails con regex**

**Opciones:**
- A) "Crea una función para validar emails usando las mejores prácticas de JavaScript moderno"
- B) **"Crea una función JavaScript llamada 'validateEmail' que tome un string como parámetro y retorne true/false. Debe validar formato de email usando regex, manejar casos edge como emails vacíos, y incluir comentarios explicativos. Proporciona también 3 ejemplos de uso."** ✅
- C) "Necesito validar emails en JavaScript con regex completo"
- D) "Función de validación de email robusta y eficiente"

*Explicación: Especifica el nombre exacto de la función, parámetros, tipo de retorno, implementación técnica y ejemplos prácticos.*

**Tarea #2: Debuggear un error de memoria en Python que causa memory leak**

**Opciones:**
- A) "Ayúdame a solucionar problemas de memoria en Python con técnicas avanzadas"
- B) **"Actúa como un senior Python developer. Analiza este código que tiene un memory leak: [código]. Identifica las causas probables, explica por qué ocurre el problema, proporciona 3 soluciones específicas con código, y sugiere herramientas para profiling de memoria."** ✅
- C) "Mi aplicación Python consume mucha memoria, necesito optimizarla"
- D) "Encuentra y arregla memory leaks en este código Python"

*Explicación: Define rol experto, incluye el código problemático, pide análisis estructurado y soluciones concretas con herramientas.*

**Tarea #3: Crear tests unitarios completos para una clase TypeScript**

**Opciones:**
- A) "Haz tests para esta clase TypeScript"
- B) **"Genera tests unitarios completos para esta clase TypeScript usando Jest. Incluye: setup/teardown, tests para todos los métodos públicos, casos edge, mocks para dependencias, assertions específicas, y cobertura de al menos 90%. Organiza en describe/it blocks."** ✅
- C) "Necesito tests unitarios con Jest"
- D) "Tests para TypeScript con buena cobertura"

*Explicación: Especifica framework de testing, estructura de tests, cobertura objetivo y organización del código de pruebas.*

**Tarea #4: Optimizar una consulta SQL que tarda más de 30 segundos**

**Opciones:**
- A) "Esta consulta SQL es muy lenta"
- B) **"Como DBA senior, analiza esta consulta SQL que tarda 30+ segundos: [query]. Identifica cuellos de botella, sugiere índices específicos, reescribe la query optimizada, explica el plan de ejecución, y proporciona métricas de mejora esperadas."** ✅
- C) "Optimiza esta query SQL lenta"
- D) "Mejora el performance de esta consulta"

*Explicación: Establece contexto de performance, rol experto, análisis técnico específico y métricas cuantificables.*

**Tarea #5: Documentar una API REST en Node.js con todos los endpoints**

**Opciones:**
- A) "Documenta esta API REST"
- B) **"Crea documentación completa para esta API REST en Node.js: [código]. Incluye: descripción de cada endpoint, métodos HTTP, parámetros requeridos/opcionales, ejemplos de request/response en JSON, códigos de estado HTTP, y casos de error. Usa formato Markdown."** ✅
- C) "Necesito docs para mi API"
- D) "Documentación de endpoints REST"

*Explicación: Define estructura específica, formato de salida, y todos los elementos técnicos necesarios para documentación profesional.*

**💡 Tips para Prompts de Programación:**
- Especifica el lenguaje y framework: JavaScript, Python, React, etc.
- Define el contexto técnico: Tipo de aplicación, arquitectura, restricciones
- Incluye el código problemático: Para debugging y optimización
- Pide estructura específica: Funciones, clases, tests, documentación
- Solicita explicaciones técnicas: Por qué funciona, mejores prácticas
- Menciona herramientas: Jest, ESLint, frameworks específicos

## 🎯 Objetivos de Aprendizaje

1. **Maestría en Prompting**: Aprender a crear prompts efectivos y específicos
2. **Detección de IA**: Desarrollar habilidades para identificar contenido generado por IA
3. **Debugging de IA**: Reconocer errores y alucinaciones comunes en código generado
4. **Evaluación de Prompts**: Entender qué hace efectivo un prompt para diferentes tareas
5. **Trabajo en Equipo**: Competir de manera sana y aprender de otros participantes