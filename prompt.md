Concepto para "Prompt Battle" y otros desafíos de IA
Estructura de la web:
Página de registro: Formulario simple donde los participantes ingresan su nombre
Sala de espera: Muestra quién está conectado y espera a que el presentador inicie el juego
Arena de desafíos: Don<PERSON> se desarrollan las diferentes pruebas
Flujo del juego:
Prompt Battle (3 minutos)
Desafío: "Crear una función que genere contraseñas seguras según parámetros específicos"
Cada participante escribe su prompt
Al finalizar el tiempo, todos los prompts se envían a la IA y se muestran los resultados
¿Humano o IA? (3 minutos)
Se muestran 5 fragmentos de código
Los participantes votan si cada uno fue escrito por humano o IA
Se revelan las respuestas correctas y se asignan puntos
Completa el código (3 minutos)
Se muestra un fragmento de código incompleto
Los participantes deben predecir cómo lo completaría una IA
Se comparan las predicciones con la respuesta real de la IA
Detector de alucinaciones (3 minutos)
Se muestra código generado por IA con errores sutiles
Los participantes identifican y señalan los errores
Gana quien encuentre más errores correctamente
Resultados finales (3 minutos)
Se suman los puntos de todas las rondas
Se muestra el ranking final
Se destacan los mejores prompts y respuestas
Ejemplo de prompt para la primera batalla:
Necesito una función en JavaScript que genere contraseñas seguras. La función debe:

Aceptar parámetros para longitud (por defecto 12 caracteres)
Incluir letras mayúsculas, minúsculas, números y símbolos
Garantizar al menos uno de cada tipo de carácter
Evitar caracteres ambiguos como 0/O o 1/l/I
Incluir una opción para hacer la contraseña pronunciable
Devolver tanto la contraseña como una puntuación de seguridad
Incluir comentarios explicativos en el código
Genera el código completo, optimizado y siguiendo las mejores prácticas de JavaScript moderno.
Este formato permite que los participantes compitan en varios desafíos cortos, manteniendo el interés y permitiendo diferentes tipos de habilidades. La plataforma podría mostrar un temporizador visible y guardar automáticamente las respuestas cuando se acabe el tiempo.

eL TIEMPo de cada desafio debe durar 3 minutos maximo
el 4to challenge debe ser elegir entre 4 prompts cual seria ideal para tareas diarias de un desarollador poner 5 ejercicos en ese desafio

El primer desafio debe ser escribir un prompt y una ia DE GEMINI evaluar un score de 1/100 para la puntuacion
Ademas d eesto la web debe ser multijugador con sockets el host puede terminar la sesion de cada desafio para dar paso al siguiente

presentar una estetica Arcade legible en todos sus textos