# Prompt Arena Challenge - Desafíos Implementados

## 🌍 Arena Global Multijugador
Una plataforma de competencia en tiempo real donde todos los jugadores luchan en la misma arena global usando Socket.IO para comunicación en tiempo real.

## 🎮 Estructura del Juego

### Fases del Juego:
1. **Registro**: Los participantes ingresan su nombre
2. **Sala de Espera**: Muestra jugadores conectados, el host puede iniciar el juego
3. **Arena de Desafíos**: 5 desafíos consecutivos con tiempo límite
4. **Resultados Finales**: Ranking y puntuaciones finales

## 🏆 Desafíos Implementados

### 1. 💻 Evaluación de Prompt Técnico (5 minutos)
**Tipo**: `prompt-evaluation`
**Descripción**: Crea un prompt para generar una aplicación de C# que consulte el Dólar Blue argentino
- Los participantes escriben un prompt técnico específico
- La IA de Gemini evalúa la calidad del prompt con un score de 0-100
- Se evalúa claridad, especificidad técnica y completitud

### 2. 📚 Información de Personajes (3 minutos)
**Tipo**: `prompt`
**Descripción**: Obtén información sobre personajes de Rick and Morty usando prompts efectivos
- Desafío: Crear el mejor prompt para obtener información de la API de Rick and Morty
- Debe incluir: nombres, especies, origen, descripción, estado y episodios
- La IA de Gemini ejecuta el prompt y evalúa la calidad de la respuesta
- Puntuación basada en completitud y precisión de la información obtenida

### 3. 🤖 ¿Humano o IA? (3 minutos)
**Tipo**: `human-ai`
**Descripción**: Identifica qué contenido fue creado por humanos y cuál por IA
- Se presentan 5 fragmentos de código
- Los participantes deben identificar si cada fragmento fue escrito por humano o IA
- Incluye ejemplos de:
  - Código con patrones típicos de IA (repetitivo, genérico)
  - Código humano con estilo personal y comentarios específicos
  - Código con errores sutiles típicos de IA
- 20 puntos por cada respuesta correcta

### 4. 🔍 Cazador de Alucinaciones (3 minutos)
**Tipo**: `errors`
**Descripción**: Encuentra funciones y métodos inventados por la IA
- Se muestra código generado por IA con errores sutiles y alucinaciones
- Los participantes deben identificar:
  - Métodos inexistentes (ej: `detectLanguageAzure` en cliente Gemini)
  - APIs incorrectas (ej: usar API key de OpenAI con cliente Gemini)
  - Funciones inventadas o mal implementadas
- Ejemplos incluyen confusiones entre servicios (Azure, OpenAI, Gemini)
- Puntuación basada en número de errores encontrados correctamente

### 5. 🎯 Elige el Mejor Prompt (3 minutos)
**Tipo**: `prompt-choice`
**Descripción**: Selecciona los prompts más efectivos para obtener mejores resultados
- Se presentan escenarios de desarrollo común
- Para cada escenario, hay 3-4 opciones de prompts
- Los participantes deben elegir el prompt más efectivo
- Escenarios incluyen:
  - Documentación de código
  - Debugging y resolución de errores
  - Explicación de conceptos técnicos
  - Optimización de código
  - Generación de tests unitarios
- 30 puntos por cada elección correcta

## ⚙️ Características Técnicas

### Sistema de Puntuación:
- **Prompt Evaluation**: 0-100 puntos (evaluado por IA)
- **Prompt Battle**: 0-100 puntos (evaluado por IA)
- **Human vs AI**: 20 puntos por respuesta correcta (máximo 100)
- **Error Detection**: Puntos variables según errores encontrados
- **Prompt Choice**: 30 puntos por respuesta correcta

### Multijugador en Tiempo Real:
- Socket.IO para comunicación en tiempo real
- Una sola arena global para todos los jugadores
- El host controla el flujo del juego
- Sincronización de temporizadores entre todos los participantes

### Interfaz Gaming:
- Estética arcade con alta legibilidad
- Animaciones y efectos visuales
- Temporizadores visibles y dinámicos
- Colores contrastantes para accesibilidad
- Diseño responsivo para diferentes dispositivos

## 🎯 Objetivos de Aprendizaje

1. **Maestría en Prompting**: Aprender a crear prompts efectivos y específicos
2. **Detección de IA**: Desarrollar habilidades para identificar contenido generado por IA
3. **Debugging de IA**: Reconocer errores y alucinaciones comunes en código generado
4. **Evaluación de Prompts**: Entender qué hace efectivo un prompt para diferentes tareas
5. **Trabajo en Equipo**: Competir de manera sana y aprender de otros participantes